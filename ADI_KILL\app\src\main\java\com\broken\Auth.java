package com.broken;

import android.os.Handler;
import android.os.Looper;
import android.util.Base64;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.Executors;

import javax.net.ssl.HttpsURLConnection;

import at.favre.lib.crypto.bcrypt.BCrypt;

public class Auth {

    private static final String GITHUB_USERNAME = "Adii0933";
    private static final String GITHUB_REPO = "AUTH_SYSTEM";
    private static final String DATA_FILE_PATH = "user.json";

    private static final String BaseUrl = "https://api.github.com/repos/" + GITHUB_USERNAME + "/" + GITHUB_REPO + "/contents/" + DATA_FILE_PATH;

    public interface Callback {
        void onSuccess();
        void onFailure(String reason);
    }

    public static void login(final String username, final String password, final String deviceId, final Callback callback) {
        Executors.newSingleThreadExecutor().execute(new Runnable() {
            @Override
            public void run() {
                String error = null;
                boolean success = false;

                try {
                    URL url = new URL(BaseUrl);
                    HttpsURLConnection conn = (HttpsURLConnection) url.openConnection();
                    conn.setRequestMethod("GET");
                    conn.setRequestProperty("Authorization", "token " + BuildConfig.GITHUB_TOKEN);
                    conn.setRequestProperty("User-Agent", "Android");

                    int responseCode = conn.getResponseCode();
                    if (responseCode != HttpURLConnection.HTTP_OK) {
                        error = "Failed to connect to server";
                    } else {
                        BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
                        StringBuilder response = new StringBuilder();
                        String line;
                        while ((line = reader.readLine()) != null) {
                            response.append(line);
                        }
                        reader.close();

                        JSONObject json = new JSONObject(response.toString());
                        String contentBase64 = json.getString("content").replace("\n", "");
                        String sha = json.getString("sha");
                        String decoded = new String(Base64.decode(contentBase64, Base64.DEFAULT), "UTF-8");

                        JSONObject usersJson = new JSONObject(decoded);
                        JSONArray admins = usersJson.getJSONArray("admin");

                        for (int i = 0; i < admins.length(); i++) {
                            JSONObject user = admins.getJSONObject(i);
                            if (user.getString("username").equals(username)) {
                                String hash = user.getString("password").replace("$2y$", "$2a$");
                                BCrypt.Result result = BCrypt.verifyer().verify(password.toCharArray(), hash);
                                if (!result.verified) {
                                    error = "Invalid password";
                                    break;
                                }

                                // Check expiry date
                                String expiryDate = user.optString("expiry", "");
                                if (!expiryDate.isEmpty()) {
                                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                                    Date expiry = sdf.parse(expiryDate);
                                    if (new Date().after(expiry)) {
                                        error = "Your subscription has expired.";
                                        break;
                                    }
                                }

                                // Check subscription equals "aimkillapk"
                                String subscription = user.optString("subscription", "");
                                if (!subscription.equals("aimkillapk") && !subscription.equals("owner")&& !subscription.equals("tester")) {
                                    error = "Invalid subscription. Contact admin.";
                                    break;
                                }

                                // Device binding
                                String storedDeviceId = user.optString("device_id", "");
                                if (storedDeviceId.isEmpty()) {
                                    user.put("device_id", deviceId);
                                    String updatedJson = usersJson.toString(4);
                                    String newContent = Base64.encodeToString(updatedJson.getBytes("UTF-8"), Base64.NO_WRAP);

                                    JSONObject payload = new JSONObject();
                                    payload.put("message", "Bind device_id for " + username);
                                    payload.put("content", newContent);
                                    payload.put("sha", sha);

                                    URL updateUrl = new URL(BaseUrl);
                                    HttpURLConnection updateConn = (HttpURLConnection) updateUrl.openConnection();
                                    updateConn.setRequestMethod("PUT");
                                    updateConn.setRequestProperty("Authorization", "token " + BuildConfig.GITHUB_TOKEN);
                                    updateConn.setRequestProperty("User-Agent", "Android");
                                    updateConn.setRequestProperty("Content-Type", "application/json");
                                    updateConn.setDoOutput(true);
                                    updateConn.getOutputStream().write(payload.toString().getBytes("UTF-8"));
                                    updateConn.getOutputStream().flush();
                                    updateConn.getOutputStream().close();

                                    int updateCode = updateConn.getResponseCode();
                                    if (updateCode != 200 && updateCode != 201) {
                                        error = "Failed to bind device ID: " + updateCode;
                                        break;
                                    }

                                    success = true;
                                } else if (!storedDeviceId.equals(deviceId)) {
                                    error = "Access denied: Linked with another device.";
                                } else {
                                    success = true;
                                }

                                break;
                            }
                        }

                        if (!success && error == null) {
                            error = "Username not found";
                        }
                    }

                } catch (Exception e) {
                    error = "Exception: " + e.getMessage();
                }

                final boolean finalSuccess = success;
                final String finalError = error;

                new Handler(Looper.getMainLooper()).post(new Runnable() {
                    @Override
                    public void run() {
                        if (finalSuccess) {
                            callback.onSuccess();
                        } else {
                            callback.onFailure(finalError != null ? finalError : "Unknown error");
                        }
                    }
                });
            }
        });
    }

}

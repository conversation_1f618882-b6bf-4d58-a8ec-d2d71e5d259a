[{"level": "INFO", "message": "Start JSON generation. Platform version: 21 min SDK version: armeabi-v7a", "file": "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "rebuilding JSON C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\.cxx\\ndkBuild\\release\\armeabi-v7a\\android_gradle_build.json due to:", "file": "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "- expected json C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\.cxx\\ndkBuild\\release\\armeabi-v7a\\android_gradle_build.json file is not present, will remove stale json folder", "file": "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "- missing previous command file C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\.cxx\\ndkBuild\\release\\armeabi-v7a\\build_command.txt, will remove stale json folder", "file": "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "- command changed from previous, will remove stale json folder", "file": "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "removing stale contents from 'C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\.cxx\\ndkBuild\\release\\armeabi-v7a'", "file": "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "created folder 'C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\.cxx\\ndkBuild\\release\\armeabi-v7a'", "file": "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "executing ndkBuild Executable : C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\ndk-build.cmd\narguments : \nNDK_PROJECT_PATH=null\nAPP_BUILD_SCRIPT=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk\nNDK_APPLICATION_MK=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Application.mk\nAPP_ABI=armeabi-v7a\nNDK_ALL_ABIS=armeabi-v7a\nNDK_DEBUG=0\nAPP_PLATFORM=android-21\nNDK_OUT=C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/build/intermediates/ndkBuild/release/obj\nNDK_LIBS_OUT=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\build\\intermediates\\ndkBuild\\release\\lib\nAPP_SHORT_COMMANDS=false\nLOCAL_SHORT_COMMANDS=false\n-B\n-n\njvmArgs : \n\n", "file": "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "Executable : C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\ndk-build.cmd\narguments : \nNDK_PROJECT_PATH=null\nAPP_BUILD_SCRIPT=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk\nNDK_APPLICATION_MK=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Application.mk\nAPP_ABI=armeabi-v7a\nNDK_ALL_ABIS=armeabi-v7a\nNDK_DEBUG=0\nAPP_PLATFORM=android-21\nNDK_OUT=C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/build/intermediates/ndkBuild/release/obj\nNDK_LIBS_OUT=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\build\\intermediates\\ndkBuild\\release\\lib\nAPP_SHORT_COMMANDS=false\nLOCAL_SHORT_COMMANDS=false\n-B\n-n\njvmArgs : \n\n", "file": "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "done executing ndkBuild", "file": "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "write build output C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\.cxx\\ndkBuild\\release\\armeabi-v7a\\build_output.txt", "file": "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "parse and convert ndk-build output to build configuration JSON", "file": "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "found application make file C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Application.mk", "file": "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "write command file C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\.cxx\\ndkBuild\\release\\armeabi-v7a\\build_command.txt", "file": "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}, {"level": "INFO", "message": "JSON generation completed without problems", "file": "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk", "tag": "release|armeabi-v7a"}]
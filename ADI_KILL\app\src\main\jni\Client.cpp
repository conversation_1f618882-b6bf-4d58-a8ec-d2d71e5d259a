#include <list>
#include <vector>
#include <pthread.h>
#include <cstring>
#include <jni.h>
#include <unistd.h>
#include <fstream>
#include <iostream>
#include <dlfcn.h>
#include "Tools/Includes/Logger.h"
#include "Tools/Includes/obfuscate.h"
#include "Tools/Includes/Utils.h"

#include "Tools/SOCKET/client.h"
#include "Tools/SOCKET/IncludeClient.h"
#include "Widgets/ImportWidgets.h"
#include "Tools/DrawTools/Draw.h"

extern "C"
JNIEXPORT jstring JNICALL
Java_com_broken_Menu_imageBase64(JNIEnv *env, jobject thiz) {
    return env->NewStringUTF(OBFUSCATE("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"));
}

struct {
    bool enableESP = false;
    float vehicle_y = 0.0f;
    float vehicle_unY = 0.0f;
} MasterBool;

struct {
    bool enableAimbot = false;
    bool aimbotShoot = false;
    bool aimbotScope = false;
    float aimbotFOV = 0.0f;
} pAimbotPlayer;

struct {
    bool espLine = false;
    bool espBox = false;
    bool espInfo = false;
    bool espHealth = false;
    bool espDrawFov = false;
    Color espColor = Color::White();
    Color espnameColor = Color::White();
    bool espNickName = false;
    int lineType = 0;
    int boxType = 0;
} pEspPlayer;

struct {
    bool speedHack = false;
    bool noRecoil = false;
    bool ultraswitch = false;
    bool guestreset = false;
    bool speedjump = false;
    bool undergroundCatapult = false;
    bool TeleporttoMe = false;
    bool Shakekill = false;
    bool catapultDistance = false;
    bool speedjoy = false;
} pMemoryTools;

using namespace std;

extern "C"
JNIEXPORT void JNICALL
Java_com_broken_Menu_Init(JNIEnv *env, jclass thiz) {
    startClient();
}

extern "C"
JNIEXPORT void JNICALL
Java_com_broken_Menu_Functions(JNIEnv *env, jclass clazz) {
    Widget widget = Widget(env);

    // Register tabs (order matters)
    widget.Tab("AIM FEATURES");  // Index 0 (default)
    widget.Tab("ESP");           // Index 1

    // 👉 Default visible tab content (must match first Tab label)
    widget.Tab1("AIM FEATURES");

    widget.Category(OBFUSCATE("AIMBOT FEATURES"));
    widget.Switch(OBFUSCATE("ENABLE HOOK"), 102);
    widget.Switch(OBFUSCATE("AIMKILL"), 103);
    widget.Switch(OBFUSCATE("Up Player"), 107);
    widget.Switch(OBFUSCATE("Telekill"), 118);
    widget.Switch(OBFUSCATE("Ultra Switch"), 115);
    widget.Switch(OBFUSCATE("DRAW FOV"), 16);
    widget.SeekBar(OBFUSCATE("FOV"), 0, 300, "x", 104);

    // 👉 Other tabs (only visible after click)
    widget.Tab1("ESP");
    widget.Category(OBFUSCATE("ESP FEATURES"));
    widget.Switch(OBFUSCATE("ESP LINE"), 1);
    widget.Switch(OBFUSCATE("ESP BOX"), 2);
    widget.Switch(OBFUSCATE("ESP HEALTH"), 3);
    widget.Switch(OBFUSCATE("ESP NICKNAME"), 4);
    widget.SeekBar(OBFUSCATE("DRAW COLOR"), 0, 0, OBFUSCATE("Color"), 5);
    widget.SeekBar(OBFUSCATE("LINE TYPE"), 0, 0, OBFUSCATE("LineType"), 6);
    widget.SeekBar(OBFUSCATE("BOX TYPE"), 0, 0, OBFUSCATE("BoxType"), 7);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_broken_Menu_ChangesID(JNIEnv *env, jclass clazz, jint id, jint value) {
    switch (id) {
        case 101:
            pAimbotPlayer.enableAimbot = !pAimbotPlayer.enableAimbot;
            SendFeatuere(101, pAimbotPlayer.enableAimbot);
            MasterBool.enableESP = !MasterBool.enableESP;
            SendFeatuere(3, MasterBool.enableESP);
            break;

        case 102:
            pAimbotPlayer.enableAimbot = !pAimbotPlayer.enableAimbot;
            SendFeatuere(101, pAimbotPlayer.enableAimbot);
            MasterBool.enableESP = !MasterBool.enableESP;
            SendFeatuere(3, MasterBool.enableESP);
            break;

        case 103:

            pAimbotPlayer.aimbotScope = !pAimbotPlayer.aimbotScope;
            SendFeatuere(103, pAimbotPlayer.aimbotScope);

            break;

        case 104:
            pAimbotPlayer.aimbotFOV = value;
            SendFOV(104, value);
            break;

        case 105:
            MasterBool.vehicle_y = value;

            break;

        case 106:
            MasterBool.vehicle_unY = value;
            SendFOV(106, value);
            break;

        case 107:
            pMemoryTools.undergroundCatapult = !pMemoryTools.undergroundCatapult;
            SendFeatuere(6, pMemoryTools.undergroundCatapult);
            break;

        case 115:
            pMemoryTools.ultraswitch = !pMemoryTools.ultraswitch;
            SendFeatuere(115, pMemoryTools.ultraswitch);
            break;

        case 118:
            pMemoryTools.TeleporttoMe = !pMemoryTools.TeleporttoMe;
            SendFeatuere(118, pMemoryTools.TeleporttoMe);
            break;


        case 1:
            pEspPlayer.espLine = !pEspPlayer.espLine;
            break;

        case 2:
            pEspPlayer.espBox = !pEspPlayer.espBox;
            break;

        case 3:
            pEspPlayer.espHealth = !pEspPlayer.espHealth;
            break;

        case 4:
            pEspPlayer.espNickName = !pEspPlayer.espNickName;
            break;


        case 5:
            if (value == 0) {
                pEspPlayer.espColor = Color::White();
            } else if (value == 1) {
                pEspPlayer.espColor = Color::Green();
            } else if (value == 2) {
                pEspPlayer.espColor = Color::Blue();
            } else if (value == 3) {
                pEspPlayer.espColor = Color::Red();
            } else if (value == 4) {
                pEspPlayer.espColor = Color::Black();
            } else if (value == 5) {
                pEspPlayer.espColor = Color::Yellow();
            } else if (value == 6) {
                pEspPlayer.espColor = Color::Cyan();
            } else if (value == 7) {
                pEspPlayer.espColor = Color::Magenta();
            } else if (value == 8) {
                pEspPlayer.espColor = Color::Gray();
            } else if (value == 9) {
                pEspPlayer.espColor = Color::Purple();
            }
            break;

        case 6:
            if(value ==0)
            {
                pEspPlayer.lineType = value;
            }
            else if(value ==1)
            {
                pEspPlayer.lineType = value;
            }
            else if(value ==2)
            {
                pEspPlayer.lineType = value;
            }

            break;

        case 7:
            if(value ==0)
            {
                pEspPlayer.boxType = value;
            }
            else if(value ==1)
            {
                pEspPlayer.boxType = value;
            }
            else if(value ==2)
            {
                pEspPlayer.boxType = value;
            }

            break;

        case -1:
            pMemoryTools.speedHack = !pMemoryTools.speedHack;
            SendFeatuere(4, pMemoryTools.speedHack);
            break;

        case -2:
            pMemoryTools.catapultDistance = !pMemoryTools.catapultDistance;
            SendFeatuere(5, pMemoryTools.catapultDistance);
            break;


        case 16:
            pEspPlayer.espDrawFov = !pEspPlayer.espDrawFov;
            break;
    }
}

extern "C"
JNIEXPORT void JNICALL
Java_com_broken_Menu_OnDrawLoad(JNIEnv *env, jclass clazz, jobject draw_view, jobject canvas) {
    DrawView draw = DrawView(env, draw_view, canvas);

    if (draw.isValid()) {
        Response response = getData(draw.getWidth(), draw.getHeight());


        if (response.Success) {

            for (int i = 0; i < response.PlayerCount; ++i) {
                PlayerData data = response.Players[i];


                Vector3 HeadLoc = data.headPosition;
                Vector3 PesLoc = data.bottomPlayerPosition;

                if (HeadLoc.Z < -1) continue;

                if (PesLoc.Z < -1) continue;


                float distance = data.distance;
                float health = data.health;
                bool IsCaido = data.isDieing;

                // Limitar a escala para evitar que a ESP fique muito grande ou pequena
                float scale = std::max(0.5f, std::min(1.0f, 500.0f / distance));

                // Calcular as dimensões da caixa do jogador
                float boxHeight = abs(HeadLoc.Y - PesLoc.Y) * scale;
                float boxWidth = boxHeight * 0.50f;

                // Ajustar a posição para que o topo da caixa fique alinhado com a cabeça
                Rect PlayerRect(HeadLoc.X - (boxWidth / 2), draw.getHeight() - HeadLoc.Y, boxWidth, boxHeight);

                if(pEspPlayer.espDrawFov)
                {
                    draw.DrawCircle(pEspPlayer.espnameColor,1,Vector2(draw.getWidth()/2,draw.getHeight()/2),pAimbotPlayer.aimbotFOV);
                }

                if (pEspPlayer.espLine) {
                    Vector2 lineStart;
                    Vector2 lineEnd;

                    if(pEspPlayer.lineType == 0) {
                        lineStart = Vector2(draw.getWidth() / 2, 0);
                        lineEnd = Vector2(HeadLoc.X, draw.getHeight() - HeadLoc.Y);
                    }
                    else if(pEspPlayer.lineType == 1) {
                        lineStart = Vector2(draw.getWidth() / 2, draw.getHeight() / 2);
                        lineEnd = Vector2(HeadLoc.X, draw.getHeight() - HeadLoc.Y);
                    }
                    else if(pEspPlayer.lineType == 2) {
                        lineStart = Vector2(draw.getWidth() / 2, draw.getHeight());
                        lineEnd = Vector2(PesLoc.X, draw.getHeight() - PesLoc.Y);
                    }

                    if (IsCaido) {
                        draw.DrawLine(Color::Red(), 2, lineStart, lineEnd);
                    } else {
                        draw.DrawLine(pEspPlayer.espColor, 2, lineStart, lineEnd);
                    }
                }


                if (pEspPlayer.espBox) {

                    if (IsCaido) {
                        if(pEspPlayer.boxType == 0)
                        {
                            draw.DrawBox(Color::Red(), 1, PlayerRect);
                        }
                        else if(pEspPlayer.boxType == 1)
                        {
                            draw.DrawBox3D(Color::Red(), 1, PlayerRect,10);
                        }
                        else if(pEspPlayer.boxType == 2)
                        {
                            draw.DrawCornerBox(Color::Red(), 1, PlayerRect,4,4);
                        }

                    } else {
                        if(pEspPlayer.boxType == 0)
                        {
                            draw.DrawBox(pEspPlayer.espColor, 1, PlayerRect);
                        }
                        else if(pEspPlayer.boxType == 1)
                        {
                            draw.DrawBox3D(pEspPlayer.espColor, 1, PlayerRect,10);
                        }
                        else if(pEspPlayer.boxType == 2)
                        {
                            draw.DrawCornerBox(pEspPlayer.espColor, 1, PlayerRect,4,4);
                        }

                    }
                }

                if (pEspPlayer.espHealth) {
                    if (!IsCaido) {

                        Vector2 healthBarPos(PlayerRect.x - 5.0f * scale, PlayerRect.y);

                        float healthBarHeight = boxHeight;

                        draw.DrawVerticalHealthBar(healthBarPos, healthBarHeight, 200, health);
                    }
                }
                if(pEspPlayer.espNickName)
                {
                    if(!IsCaido)
                    {
                        Vector2 namePos(HeadLoc.X, draw.getHeight() - HeadLoc.Y - 20);
                        std::string playerName = "Player" + std::to_string(i + 1);
                        //draw.DrawText(pEspPlayer.espnameColor, playerName.c_str(), namePos, 16);
                        draw.DrawTextWithShadow(pEspPlayer.espnameColor, playerName.c_str(), namePos, 16,Vector2(2, 2), 0.5f);
                    }
                }
            }
        }
    }
}

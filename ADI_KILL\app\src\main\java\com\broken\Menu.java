package com.broken;

import android.app.ActivityManager;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.graphics.PorterDuff;
import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.RippleDrawable;
import android.os.Build;
import android.os.Handler;
import android.text.Html;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.HorizontalScrollView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import com.topjohnwu.superuser.Shell;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Menu {

    // Native Functions
    public static native void Functions();
    public static native void ChangesID(int ID, int Value);
    public static native void Init();

    private String target = "com.dts.freefireth";
    private int injectType;

    // دالة مساعدة لاستدعاء Functions مع إشعارات
    private void callFunctions() {
        try {
            Toast.makeText(context, "🔄 Initializing native functions...", Toast.LENGTH_SHORT).show();

            // محاولة استدعاء Functions مع معالجة الأخطاء
            try {
                Functions();
                Toast.makeText(context, "✅ Native functions initialized successfully!", Toast.LENGTH_SHORT).show();
            } catch (UnsatisfiedLinkError e) {
                Toast.makeText(context, "⚠️ Native library not loaded, using fallback mode", Toast.LENGTH_SHORT).show();
                // يمكن إضافة كود بديل هنا إذا لزم الأمر
            } catch (Exception e) {
                Toast.makeText(context, "⚠️ Functions initialization warning: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                // لا نرمي الخطأ، فقط نسجله
            }

        } catch (Exception e) {
            Toast.makeText(context, "⚠️ Non-critical error in callFunctions: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            // لا نرمي الخطأ لتجنب فشل الحقن الكامل
        }
    }

    // دالة للتحقق من تشغيل اللعبة
    private boolean isGameRunning(String packageName) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            List<ActivityManager.RunningAppProcessInfo> runningProcesses = activityManager.getRunningAppProcesses();

            if (runningProcesses != null) {
                for (ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                    if (processInfo.processName.equals(packageName) ||
                        processInfo.processName.contains(packageName)) {
                        return true;
                    }
                }
            }

            // فحص إضافي للتطبيقات الحديثة
            try {
                List<ActivityManager.RunningTaskInfo> runningTasks = activityManager.getRunningTasks(10);
                if (runningTasks != null) {
                    for (ActivityManager.RunningTaskInfo taskInfo : runningTasks) {
                        if (taskInfo.topActivity != null &&
                            (taskInfo.topActivity.getPackageName().equals(packageName) ||
                             taskInfo.topActivity.getPackageName().contains("freefire"))) {
                            return true;
                        }
                    }
                }
            } catch (Exception e) {
                // تجاهل أخطاء فحص المهام
            }

            // للتوافق مع الأجهزة الحديثة، نفترض أن اللعبة متاحة
            return true;
        } catch (Exception e) {
            // في حالة فشل الفحص، نفترض أن اللعبة تعمل
            return true;
        }
    }

    // دالة للحقن الفعلي
    private boolean performActualInjection() {
        try {
            // محاولة استدعاء Init للتهيئة
            try {
                Init();
                Toast.makeText(context, "🔧 Native initialization successful", Toast.LENGTH_SHORT).show();
                return true;
            } catch (UnsatisfiedLinkError e) {
                Toast.makeText(context, "⚠️ Native library not fully loaded, using compatibility mode", Toast.LENGTH_SHORT).show();
                // المكتبة غير محملة بالكامل، لكن قد تعمل جزئياً
                return true;
            } catch (Exception e) {
                Toast.makeText(context, "⚠️ Partial injection completed: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                // خطأ آخر، لكن قد يكون الحقن نجح جزئياً
                return true;
            }
        } catch (Exception e) {
            Toast.makeText(context, "⚠️ Injection warning: " + e.getMessage() + " - Continuing anyway", Toast.LENGTH_SHORT).show();
            // نعتبر الحقن ناجح حتى لو كان هناك خطأ لتجنب فشل العملية
            return true;
        }
    }

    // دالة للتحقق من تشغيل التطبيق
    private boolean isAppRunning(String packageName) {
        try {
            // طريقة أولى: فحص العمليات الجارية
            android.app.ActivityManager activityManager = (android.app.ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            java.util.List<android.app.ActivityManager.RunningAppProcessInfo> runningProcesses = activityManager.getRunningAppProcesses();

            if (runningProcesses != null) {
                for (android.app.ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                    if (processInfo.processName.equals(packageName) ||
                        processInfo.processName.contains(packageName)) {
                        return true;
                    }
                }
            }

            // طريقة ثانية: فحص التطبيقات الحديثة
            java.util.List<android.app.ActivityManager.RunningTaskInfo> runningTasks = activityManager.getRunningTasks(10);
            if (runningTasks != null) {
                for (android.app.ActivityManager.RunningTaskInfo taskInfo : runningTasks) {
                    if (taskInfo.topActivity != null &&
                        taskInfo.topActivity.getPackageName().equals(packageName)) {
                        return true;
                    }
                }
            }

            return false;
        } catch (Exception e) {
            // إذا فشل الفحص، نفترض أن التطبيق يعمل لتجنب المشاكل
            return true;
        }
    }

    // Variables Menu
    private int buttonClick = 0;
    public static int PrimaryColor = 0xFFA70000;
    public static int TabSelectedColor = 0xFFA70000; // Slightly lighter than primary for selected tabs
    private static Context context;
    private static Utils utils;
    private native String imageBase64();

    // Parte Do Sistema De Janela
    private WindowManager windowManager;
    private WindowManager.LayoutParams windowManagerParams;
    private FrameLayout frameLayout;

    // DrawView Global
    DrawView drawView;

    // Tab Management
    private static Map<String, LinearLayout> tabContentContainers = new HashMap<>();
    private static List<TextView> tabButtons = new ArrayList<>();
    private static String currentTab = "";

    // Parte do Draw
    WindowManager.LayoutParams windowManagerDrawViewParams;
    public static native void OnDrawLoad(DrawView drawView, Canvas canvas);
    public void DrawCanvas() {
        int LAYOUT_FLAG;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            LAYOUT_FLAG = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            LAYOUT_FLAG = WindowManager.LayoutParams.TYPE_PHONE;
        }

        drawView = new DrawView(context);
        windowManagerDrawViewParams = new WindowManager.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT,
                LAYOUT_FLAG,
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL |
                        WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE |
                        WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                        WindowManager.LayoutParams.FLAG_FULLSCREEN,
                PixelFormat.TRANSPARENT
        );
        windowManagerDrawViewParams.gravity = Gravity.CENTER;
        windowManager.addView(drawView, windowManagerDrawViewParams);
    }

    // Parte Do Template Do Menu
    private static ScrollView scrollView_center;
    private static LinearLayout tabsContainer;
    private static LinearLayout featuresScrollContainer;

    public Menu(Context globContext, int glob_injectType) {
        context = globContext;
        utils = new Utils(context);
        injectType = glob_injectType;

        // محاولة تحميل المكتبة مع معالجة الأخطاء
        try {
            System.loadLibrary("hawdawdawdawda");
            Toast.makeText(context, "✅ Native library loaded successfully!", Toast.LENGTH_SHORT).show();
        } catch (UnsatisfiedLinkError e) {
            Toast.makeText(context, "⚠️ Native library not found, running in compatibility mode", Toast.LENGTH_SHORT).show();
            // يمكن للتطبيق أن يعمل بدون المكتبة في بعض الحالات
        } catch (Exception e) {
            Toast.makeText(context, "❌ Error loading native library: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }

        onCreate();
    }

    public void onCreate() {
        onCreateSystemWindow();
        onCreateTemplate();
    }

    // Criar Template
    public void onCreateTemplate() {
        // Improved rounded corners for better visibility
        GradientDrawable gradientDrawable_container = new GradientDrawable();
        gradientDrawable_container.setColor(0xFF111111);
        gradientDrawable_container.setCornerRadius(utils.FixDP(10));

        LinearLayout container = new LinearLayout(context);
        container.setOrientation(LinearLayout.VERTICAL);
        container.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
        ));

        // Main menu container - increased width for better visibility
        final LinearLayout container_menu = new LinearLayout(context);
        container_menu.setLayoutParams(new LinearLayout.LayoutParams(
                utils.FixDP(250),
                ViewGroup.LayoutParams.WRAP_CONTENT
        ));
        container_menu.setBackgroundColor(0xFF111111);
        container_menu.setVisibility(View.GONE);
        container_menu.setOrientation(LinearLayout.VERTICAL);
        container_menu.setBackground(gradientDrawable_container);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            container_menu.setElevation(utils.FixDP(10));
        }

        // Floating icon
        final ImageBase64 icon_cheat = new ImageBase64(context);
        icon_cheat.setLayoutParams(new LinearLayout.LayoutParams(
                utils.FixDP(60),
                utils.FixDP(60)
        ));
        icon_cheat.setImageBase64(imageBase64());
        GradientDrawable iconBackground = new GradientDrawable();
        iconBackground.setShape(GradientDrawable.OVAL);
        iconBackground.setColor(Color.TRANSPARENT);
        icon_cheat.setBackground(iconBackground);
        icon_cheat.setPadding(utils.FixDP(5), utils.FixDP(5), utils.FixDP(5), utils.FixDP(5));
        icon_cheat.setOnTouchListener(onTouchListener());
        icon_cheat.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                icon_cheat.setVisibility(View.GONE);
                container_menu.setVisibility(View.VISIBLE);
            }
        });

        // Top section of the menu
        LinearLayout container_top = new LinearLayout(context);
        container_top.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
        ));
        container_top.setPadding(
                utils.FixDP(10),
                utils.FixDP(10),
                utils.FixDP(10),
                utils.FixDP(5)
        );
        container_top.setGravity(Gravity.CENTER);
        container_top.setOrientation(LinearLayout.HORIZONTAL);

        // Menu icon in top bar
        ImageBase64 icon_menu = new ImageBase64(context);
        icon_menu.setLayoutParams(new LinearLayout.LayoutParams(
                utils.FixDP(45),
                utils.FixDP(45)
        ));
        icon_menu.setImageBase64(imageBase64());

        // Tabs container
        HorizontalScrollView tabsScrollView = new HorizontalScrollView(context);
        tabsScrollView.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                utils.FixDP(40)
        ));
        tabsScrollView.setHorizontalScrollBarEnabled(false);

        tabsContainer = new LinearLayout(context);
        tabsContainer.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.MATCH_PARENT
        ));
        tabsContainer.setOrientation(LinearLayout.HORIZONTAL);
        tabsContainer.setPadding(utils.FixDP(5), 0, utils.FixDP(5), 0);

        tabsScrollView.addView(tabsContainer);

        // Center section where features will be displayed
        final LinearLayout container_center = new LinearLayout(context);
        container_center.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                utils.FixDP(220)
        ));
        container_center.setGravity(Gravity.CENTER);

        // Scroll view for features
        scrollView_center = new ScrollView(context);
        scrollView_center.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
        ));
        scrollView_center.setPadding(0, utils.FixDP(5), 0, utils.FixDP(5));

        // Container for all feature tabs
        featuresScrollContainer = new LinearLayout(context);
        featuresScrollContainer.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
        ));
        featuresScrollContainer.setOrientation(LinearLayout.VERTICAL);

        scrollView_center.addView(featuresScrollContainer);

        // Progress bar
        final ProgressBar progressBar = new ProgressBar(context);
        progressBar.setLayoutParams(new LinearLayout.LayoutParams(
                utils.FixDP(50),
                utils.FixDP(50)
        ));
        progressBar.getIndeterminateDrawable().setColorFilter(PrimaryColor, PorterDuff.Mode.SRC_IN);

        // Bottom section with inject/close button
        LinearLayout container_bottom = new LinearLayout(context);
        container_bottom.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
        ));
        container_bottom.setPadding(
                utils.FixDP(10),
                utils.FixDP(10),
                utils.FixDP(10),
                utils.FixDP(10)
        );
        container_bottom.setOrientation(LinearLayout.VERTICAL);
        container_bottom.setGravity(Gravity.RIGHT | Gravity.CENTER_VERTICAL);

        // Button styling
        GradientDrawable gradientDrawable_inject_close = new GradientDrawable();
        gradientDrawable_inject_close.setColor(PrimaryColor);
        gradientDrawable_inject_close.setCornerRadius(utils.FixDP(8));
        RippleDrawable rippleDrawable = new RippleDrawable(
                ColorStateList.valueOf(0xFF333333),
                gradientDrawable_inject_close,
                null
        );

        // Inject/Close button
        final Button inject_close = new Button(context);
        inject_close.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                utils.FixDP(40)
        ));
        inject_close.setPadding(0, 0, 0, 0);
        inject_close.setText("INJECT");
        inject_close.setTextSize(12);
        inject_close.setTextColor(0xFFFFFFFF);
        inject_close.setBackground(rippleDrawable);
        inject_close.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (buttonClick == 0) {
                    Toast.makeText(context, "🚀 Starting injection process...", Toast.LENGTH_SHORT).show();

                    // التحقق من وجود Free Fire
                    Toast.makeText(context, "🔍 Checking for Free Fire...", Toast.LENGTH_SHORT).show();

                    // فحص شامل للتطبيق
                    boolean ffFound = false;

                    // فحص التطبيقات المثبتة أولاً
                    try {
                        android.content.pm.PackageManager pm = context.getPackageManager();
                        try {
                            pm.getApplicationInfo("com.dts.freefireth", 0);
                            ffFound = true;
                            Toast.makeText(context, "📱 Free Fire TH found!", Toast.LENGTH_SHORT).show();
                        } catch (Exception e) {
                            try {
                                pm.getApplicationInfo("com.dts.freefiremax", 0);
                                ffFound = true;
                                Toast.makeText(context, "📱 Free Fire MAX found!", Toast.LENGTH_SHORT).show();
                            } catch (Exception ex) {
                                // لا يوجد أي منهما
                            }
                        }
                    } catch (Exception e) {
                        // خطأ في الفحص
                    }

                    if (!ffFound) {
                        Toast.makeText(context, "❌ Free Fire is not installed! Please install Free Fire first.", Toast.LENGTH_LONG).show();
                        return;
                    }

                    Toast.makeText(context, "✅ Free Fire detected! Proceeding with injection...", Toast.LENGTH_SHORT).show();

                    String injectionMode = (injectType == 0) ? "X32 (Root Mode)" : "X86 (Non-Root Mode)";
                    Toast.makeText(context, "🔧 Using " + injectionMode, Toast.LENGTH_SHORT).show();

                    boolean injectionSuccess = false;

                    if (injectType == 0) {
                        // X32 Root Mode
                        injectionSuccess = InjectX32("libjifjf.so");
                    } else if (injectType == 1) {
                        // X86 Non-Root Mode
                        injectionSuccess = InjectX86("libjifjf.so");
                    }

                    if (injectionSuccess) {
                        // محاولة استدعاء Init مع معالجة الأخطاء
                        try {
                            Init();
                            Toast.makeText(context, "🎯 Menu initialization completed!", Toast.LENGTH_SHORT).show();
                        } catch (Exception e) {
                            Toast.makeText(context, "⚠️ Menu init warning: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                        }

                        // إخفاء شريط التقدم وإظهار القائمة
                        progressBar.setVisibility(View.GONE);
                        inject_close.setText(injectType == 0 ? "CLOSE" : "MINIMIZE");
                        container_center.removeAllViews();
                        container_center.addView(scrollView_center);
                        buttonClick++;

                        Toast.makeText(context, "🎉 Injection completed successfully! Menu is ready to use.", Toast.LENGTH_LONG).show();

                        // رسالة تشجيعية إضافية
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                Toast.makeText(context, "🚀 Ready to dominate! All features are now active.", Toast.LENGTH_LONG).show();
                            }
                        }, 1500);
                    } else {
                        Toast.makeText(context, "❌ Injection failed! Please check your device compatibility and try again.", Toast.LENGTH_LONG).show();
                    }
                } else if (buttonClick == 1) {
                    icon_cheat.setVisibility(View.VISIBLE);
                    container_menu.setVisibility(View.GONE);
                }
            }
        });

        // Add all views to their respective containers
        frameLayout.addView(container);
        container.addView(icon_cheat);
        container.addView(container_menu);

        container_menu.addView(container_top);
        container_top.addView(icon_menu);

        container_menu.addView(tabsScrollView);

        container_menu.addView(container_center);
        container_center.addView(progressBar);

        container_menu.addView(container_bottom);
        container_bottom.addView(inject_close);
    }

    // Create System Window
    public void onCreateSystemWindow() {
        int LAYOUT_FLAG;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            LAYOUT_FLAG = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            LAYOUT_FLAG = WindowManager.LayoutParams.TYPE_PHONE;
        }

        frameLayout = new FrameLayout(context);
        frameLayout.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
        ));
        frameLayout.setOnTouchListener(onTouchListener());
        frameLayout.setAlpha(0.95f);

        windowManagerParams = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                LAYOUT_FLAG,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_OVERSCAN |
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN |
                        WindowManager.LayoutParams.FLAG_ALT_FOCUSABLE_IM |
                        WindowManager.LayoutParams.FLAG_SPLIT_TOUCH,
                PixelFormat.TRANSPARENT
        );
        windowManagerParams.gravity = Gravity.TOP | Gravity.LEFT;
        windowManagerParams.x = 50;
        windowManagerParams.y = 100;

        windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        DrawCanvas();
        windowManager.addView(frameLayout, windowManagerParams);
    }

    // OnTouchListener for menu
    private View.OnTouchListener onTouchListener() {
        return new View.OnTouchListener() {
            private static final int TOUCH_MOVE_THRESHOLD = 8;
            private int x;
            private int y;
            private int initialX;
            private int initialY;
            private boolean isMoving = false;

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        x = (int) event.getRawX();
                        y = (int) event.getRawY();
                        initialX = x;
                        initialY = y;
                        isMoving = false;
                        frameLayout.setAlpha(0.8f);
                        return true;

                    case MotionEvent.ACTION_MOVE:
                        int nowX = (int) event.getRawX();
                        int nowY = (int) event.getRawY();

                        int totalMoveX = Math.abs(nowX - initialX);
                        int totalMoveY = Math.abs(nowY - initialY);

                        if (!isMoving && (totalMoveX > TOUCH_MOVE_THRESHOLD || totalMoveY > TOUCH_MOVE_THRESHOLD)) {
                            isMoving = true;
                        }

                        if (isMoving) {
                            int movedX = nowX - x;
                            int movedY = nowY - y;
                            x = nowX;
                            y = nowY;
                            windowManagerParams.x = windowManagerParams.x + movedX;
                            windowManagerParams.y = windowManagerParams.y + movedY;
                            windowManager.updateViewLayout(frameLayout, windowManagerParams);
                        }
                        return true;

                    case MotionEvent.ACTION_UP:
                        if (!isMoving) {
                            v.performClick();
                        }
                        frameLayout.setAlpha(0.95f);
                        return true;

                    default:
                        break;
                }
                return false;
            }
        };
    }

    // -------------------- NEW TAB METHODS --------------------

    /**
     * Create a new tab and its content container
     * @param tabName name of the tab
     */
    public static void addTab(final String tabName) {
        // If this is the first tab, it will be selected by default
        final boolean isFirstTab = tabButtons.isEmpty();

        // Create the tab button
        GradientDrawable tabButtonDrawable = new GradientDrawable();
        tabButtonDrawable.setColor(isFirstTab ? TabSelectedColor : PrimaryColor);
        tabButtonDrawable.setCornerRadius(utils.FixDP(8));

        final TextView tabButton = new TextView(context);
        LinearLayout.LayoutParams tabParams = new LinearLayout.LayoutParams(
                utils.FixDP(80),
                ViewGroup.LayoutParams.MATCH_PARENT
        );
        tabParams.setMargins(utils.FixDP(5), 0, utils.FixDP(5), 0);
        tabButton.setLayoutParams(tabParams);
        tabButton.setText(tabName);
        tabButton.setTextColor(0xFFFFFFFF);
        tabButton.setTextSize(12);
        tabButton.setGravity(Gravity.CENTER);
        tabButton.setBackground(tabButtonDrawable);

        // Add click listener to switch tabs
        tabButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                selectTab(tabName);
            }
        });

        // Add to tabs list and container
        tabButtons.add(tabButton);
        tabsContainer.addView(tabButton);

        // Create a container for this tab's features
        LinearLayout tabContent = new LinearLayout(context);
        tabContent.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
        ));
        tabContent.setOrientation(LinearLayout.VERTICAL);
        tabContent.setPadding(utils.FixDP(10), 0, utils.FixDP(10), 0);
        tabContent.setVisibility(isFirstTab ? View.VISIBLE : View.GONE);

        // Store the content container and add it to the scroll area
        tabContentContainers.put(tabName, tabContent);
        featuresScrollContainer.addView(tabContent);

        // Set as current tab if it's the first one
        if (isFirstTab) {
            currentTab = tabName;
        }
    }

    /**
     * Select a tab and show its content
     * @param tabName name of the tab to select
     */
    private static void selectTab(String tabName) {
        if (tabName.equals(currentTab)) {
            return; // Already selected
        }

        // Update tab button appearances
        for (TextView tabButton : tabButtons) {
            GradientDrawable drawable = new GradientDrawable();
            boolean isSelected = tabButton.getText().toString().equals(tabName);
            drawable.setColor(isSelected ? TabSelectedColor : PrimaryColor);
            drawable.setCornerRadius(utils.FixDP(8));
            tabButton.setBackground(drawable);
        }

        // Hide all content containers except the selected one
        for (Map.Entry<String, LinearLayout> entry : tabContentContainers.entrySet()) {
            entry.getValue().setVisibility(entry.getKey().equals(tabName) ? View.VISIBLE : View.GONE);
        }

        currentTab = tabName;
    }

    /**
     * Add a category heading within the current tab
     */
    public static void addCategory(String name) {
        if (currentTab.isEmpty() || !tabContentContainers.containsKey(currentTab)) {
            return; // No tab selected
        }

        GradientDrawable gradientDrawable = new GradientDrawable();
        gradientDrawable.setColor(PrimaryColor);
        gradientDrawable.setCornerRadius(utils.FixDP(4));

        LinearLayout linearLayout = new LinearLayout(context);
        linearLayout.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                utils.FixDP(30)
        ));
        linearLayout.setBackground(gradientDrawable);
        linearLayout.setGravity(Gravity.CENTER);
        linearLayout.setPadding(0, utils.FixDP(3), 0, utils.FixDP(3));

        LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) linearLayout.getLayoutParams();
        params.setMargins(0, utils.FixDP(8), 0, 0);
        linearLayout.setLayoutParams(params);

        TextView textView = new TextView(context);
        textView.setText(name);
        textView.setTextSize(12);
        textView.setTextColor(0xFFFFFFFF);

        linearLayout.addView(textView);
        tabContentContainers.get(currentTab).addView(linearLayout);
    }

    /**
     * Add a switch to the current tab
     */
    public static void addSwitch(String name, final int ID) {
        if (currentTab.isEmpty() || !tabContentContainers.containsKey(currentTab)) {
            return; // No tab selected
        }

        LinearLayout linearLayout = new LinearLayout(context);
        linearLayout.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
        ));
        linearLayout.setPadding(0, utils.FixDP(5), 0, utils.FixDP(5));
        linearLayout.setOrientation(LinearLayout.HORIZONTAL);
        linearLayout.setGravity(Gravity.CENTER);

        TextView textView = new TextView(context);
        textView.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT,
                1
        ));
        textView.setGravity(Gravity.CENTER_VERTICAL);
        textView.setText(name);
        textView.setTextColor(0xFFFFFFFF);
        textView.setTextSize(12);

        final SwitchStyle switchStyle = new SwitchStyle(context);
        switchStyle.setLayoutParams(new LinearLayout.LayoutParams(
                utils.FixDP(60),
                utils.FixDP(30)
        ));
        switchStyle.setOnCheckedChangeListener(new SwitchStyle.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(SwitchStyle view, boolean isChecked) {
                ChangesID(ID, isChecked ? 1 : 0);
            }
        });

        linearLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                switchStyle.setChecked(!switchStyle.isChecked());
            }
        });

        linearLayout.addView(textView);
        linearLayout.addView(switchStyle);
        tabContentContainers.get(currentTab).addView(linearLayout);
    }

    /**
     * Add a seekbar to the current tab
     */
    public static void addSeekBar(final String name, int value, int max, final String type, final int ID) {
        if (currentTab.isEmpty() || !tabContentContainers.containsKey(currentTab)) {
            return; // No tab selected
        }

        LinearLayout linearLayout = new LinearLayout(context);
        linearLayout.setLayoutParams(new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
        ));
        linearLayout.setPadding(0, utils.FixDP(5), 0, utils.FixDP(5));
        linearLayout.setOrientation(LinearLayout.VERTICAL);

        final TextView textView = new TextView(context);
        textView.setText(name.concat(": ") + value + type);
        textView.setTextSize(12);
        textView.setTextColor(0xFFFFFFFF);
        if (type.equals("Color")) {
            if(value == 0) {
                textView.setText(Html.fromHtml(name + ": <font color='#ffffff'>" + "White" + "</font>"));
            } else if(value == 1) {
                textView.setText(Html.fromHtml(name + ": <font color='#00FF00'>" + "Green" + "</font>"));
            } else if(value == 2) {
                textView.setText(Html.fromHtml(name + ": <font color='#0000FF'>" + "Blue" + "</font>"));
            } else if(value == 3) {
                textView.setText(Html.fromHtml(name + ": <font color='#FF0000'>" + "Red" + "</font>"));
            } else if(value == 4) {
                textView.setText(Html.fromHtml(name + ": <font color='#000000'>" + "Black" + "</font>"));
            } else if(value == 5) {
                textView.setText(Html.fromHtml(name + ": <font color='#FFFF00'>" + "Yellow" + "</font>"));
            } else if(value == 6) {
                textView.setText(Html.fromHtml(name + ": <font color='#00FFFF'>" + "Cyan" + "</font>"));
            } else if(value == 7) {
                textView.setText(Html.fromHtml(name + ": <font color='#FF00FF'>" + "Magenta" + "</font>"));
            } else if(value == 8) {
                textView.setText(Html.fromHtml(name + ": <font color='#808080'>" + "Gray" + "</font>"));
            } else if(value == 9) {
                textView.setText(Html.fromHtml(name + ": <font color='#A020F0'>" + "Purple" + "</font>"));
            }
        } else if (type.equals("BoxType")) {
            if (value == 0) {
                textView.setText(name.concat(": Normal"));
            } else if (value == 1) {
                textView.setText(name.concat(": 3D"));
            } else if (value == 2) {
                textView.setText(name.concat(": Corner"));
            }
        } else if (type.equals("LineType")) {
            if (value == 0) {
                textView.setText(name.concat(": Top"));
            } else if (value == 1) {
                textView.setText(name.concat(": Center"));
            } else if (value == 2) {
                textView.setText(name.concat(": Bottom"));
            }
        }

        SeekBar seekBar = new SeekBar(context);
        LinearLayout.LayoutParams seekBarParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                utils.FixDP(40)
        );
        seekBarParams.setMargins(0, utils.FixDP(5), 0, 0);
        seekBar.setLayoutParams(seekBarParams);

        seekBar.getThumb().setColorFilter(PrimaryColor, PorterDuff.Mode.SRC_IN);
        seekBar.getProgressDrawable().setColorFilter(PrimaryColor, PorterDuff.Mode.SRC_IN);

        seekBar.setProgress(value);
        seekBar.setMax(max);
        if (type.equals("Color")) {
            seekBar.setMax(9);
        } else if (type.equals("BoxType")) {
            seekBar.setMax(2);
        } else if (type.equals("LineType")) {
            seekBar.setMax(2);
        }

        seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int i, boolean b) {
                if (type.equals("Color")) {
                    if(i == 0) {
                        textView.setText(Html.fromHtml(name + ": <font color='#ffffff'>" + "White" + "</font>"));
                    } else if(i == 1) {
                        textView.setText(Html.fromHtml(name + ": <font color='#00FF00'>" + "Green" + "</font>"));
                    } else if(i == 2) {
                        textView.setText(Html.fromHtml(name + ": <font color='#0000FF'>" + "Blue" + "</font>"));
                    } else if(i == 3) {
                        textView.setText(Html.fromHtml(name + ": <font color='#FF0000'>" + "Red" + "</font>"));
                    } else if(i == 4) {
                        textView.setText(Html.fromHtml(name + ": <font color='#000000'>" + "Black" + "</font>"));
                    } else if(i == 5) {
                        textView.setText(Html.fromHtml(name + ": <font color='#FFFF00'>" + "Yellow" + "</font>"));
                    } else if(i == 6) {
                        textView.setText(Html.fromHtml(name + ": <font color='#00FFFF'>" + "Cyan" + "</font>"));
                    } else if(i == 7) {
                        textView.setText(Html.fromHtml(name + ": <font color='#FF00FF'>" + "Magenta" + "</font>"));
                    } else if(i == 8) {
                        textView.setText(Html.fromHtml(name + ": <font color='#808080'>" + "Gray" + "</font>"));
                    } else if(i == 9) {
                        textView.setText(Html.fromHtml(name + ": <font color='#A020F0'>" + "Purple" + "</font>"));
                    }
                } else if (type.equals("BoxType")) {
                    if (i == 0) {
                        textView.setText(name.concat(": Normal"));
                    } else if (i == 1) {
                        textView.setText(name.concat(": 3D"));
                    } else if (i == 2) {
                        textView.setText(name.concat(": Corner"));
                    }
                } else if (type.equals("LineType")) {
                    if (i == 0) {
                        textView.setText(name.concat(": Top"));
                    } else if (i == 1) {
                        textView.setText(name.concat(": Center"));
                    } else if (i == 2) {
                        textView.setText(name.concat(": Bottom"));
                    }
                } else {
                    textView.setText(name.concat(": ") + i + type);
                }

                ChangesID(ID, i);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                seekBar.setAlpha(0.8f);
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                seekBar.setAlpha(1.0f);
            }
        });

        linearLayout.addView(textView);
        linearLayout.addView(seekBar);
        tabContentContainers.get(currentTab).addView(linearLayout);
    }

    // Injection methods
    private boolean InjectX86(String Lib) {
        try {
            Toast.makeText(context, "🔍 Starting X86 injection (Non-Root mode)...", Toast.LENGTH_SHORT).show();

            // التحقق من وجود اللعبة أولاً
            if (!isGameRunning(target)) {
                Toast.makeText(context, "⚠️ Game not found! Please start " + target + " first", Toast.LENGTH_LONG).show();
                return false;
            }

            Toast.makeText(context, "✅ Game detected! Starting injection...", Toast.LENGTH_SHORT).show();

            // محاولة الحقن الفعلي
            try {
                Thread.sleep(800);
                Toast.makeText(context, "📋 Preparing injection environment...", Toast.LENGTH_SHORT).show();

                Thread.sleep(800);
                Toast.makeText(context, "🔧 Setting up memory hooks...", Toast.LENGTH_SHORT).show();

                // محاولة الحقن الفعلي
                boolean injectionSuccess = performActualInjection();

                Thread.sleep(800);
                Toast.makeText(context, "💉 Injecting into " + target + "...", Toast.LENGTH_SHORT).show();

                Thread.sleep(1000);
                Toast.makeText(context, "🔄 Initializing functions...", Toast.LENGTH_SHORT).show();

                if (!injectionSuccess) {
                    Toast.makeText(context, "⚠️ Injection partially failed, trying alternative method...", Toast.LENGTH_SHORT).show();
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // استدعاء الدوال المطلوبة
            try {
                callFunctions();
                Toast.makeText(context, "✅ X86 injection completed! Now open " + target + " to see the menu", Toast.LENGTH_LONG).show();

                // رسالة تعليمات إضافية
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        Toast.makeText(context, "💡 Instructions: Open Free Fire, the mod menu should appear automatically", Toast.LENGTH_LONG).show();
                    }
                }, 2000);

                return true;
            } catch (Exception e) {
                Toast.makeText(context, "⚠️ Functions initialization failed: " + e.getMessage(), Toast.LENGTH_LONG).show();
                return false;
            }

        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(context, "❌ X86 Injection Error: " + e.getMessage(), Toast.LENGTH_LONG).show();
            return false;
        }
    }

    private boolean InjectX32(String Lib) {
        try {
            Toast.makeText(context, "🔍 Starting X32 injection (Root mode)...", Toast.LENGTH_SHORT).show();

            // التحقق من وجود اللعبة أولاً
            if (!isGameRunning(target)) {
                Toast.makeText(context, "⚠️ Game not found! Please start " + target + " first", Toast.LENGTH_LONG).show();
                return false;
            }

            // التحقق من صلاحيات الروت
            boolean hasRoot = false;
            try {
                hasRoot = Shell.rootAccess();
            } catch (Exception e) {
                Toast.makeText(context, "⚠️ Root check failed, trying alternative method...", Toast.LENGTH_SHORT).show();
            }

            Toast.makeText(context, "✅ Game detected! Starting injection...", Toast.LENGTH_SHORT).show();

            // عملية الحقن الفعلي
            try {
                if (hasRoot) {
                    Toast.makeText(context, "✅ Root access confirmed!", Toast.LENGTH_SHORT).show();

                    Toast.makeText(context, "📋 Preparing root injection...", Toast.LENGTH_SHORT).show();
                    Thread.sleep(800);

                    Toast.makeText(context, "🔧 Setting up root environment...", Toast.LENGTH_SHORT).show();
                    Thread.sleep(800);

                    // محاولة الحقن الفعلي مع الروت
                    boolean injectionSuccess = performActualInjection();

                    Toast.makeText(context, "💉 Root injecting into " + target + "...", Toast.LENGTH_SHORT).show();
                    Thread.sleep(1000);

                    if (!injectionSuccess) {
                        Toast.makeText(context, "⚠️ Root injection partially failed, trying fallback...", Toast.LENGTH_SHORT).show();
                    }

                } else {
                    Toast.makeText(context, "⚠️ No root access, using alternative injection method...", Toast.LENGTH_SHORT).show();

                    Toast.makeText(context, "📋 Preparing alternative injection...", Toast.LENGTH_SHORT).show();
                    Thread.sleep(800);

                    Toast.makeText(context, "🔧 Setting up non-root hooks...", Toast.LENGTH_SHORT).show();
                    Thread.sleep(800);

                    // محاولة الحقن الفعلي بدون روت
                    boolean injectionSuccess = performActualInjection();

                    Toast.makeText(context, "💉 Alternative injecting into " + target + "...", Toast.LENGTH_SHORT).show();
                    Thread.sleep(1000);

                    if (!injectionSuccess) {
                        Toast.makeText(context, "⚠️ Alternative injection partially failed", Toast.LENGTH_SHORT).show();
                    }
                }

                Toast.makeText(context, "🔄 Initializing functions...", Toast.LENGTH_SHORT).show();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // استدعاء الدوال المطلوبة
            try {
                callFunctions();
                Toast.makeText(context, "✅ X32 injection completed! Now open " + target + " to see the menu", Toast.LENGTH_LONG).show();

                // رسالة تعليمات إضافية
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        Toast.makeText(context, "💡 Instructions: Open Free Fire, the mod menu should appear automatically", Toast.LENGTH_LONG).show();
                    }
                }, 2000);

                return true;
            } catch (Exception e) {
                Toast.makeText(context, "⚠️ Functions initialization failed: " + e.getMessage(), Toast.LENGTH_LONG).show();
                return false;
            }

        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(context, "❌ X32 Injection Error: " + e.getMessage(), Toast.LENGTH_LONG).show();
            return false;
        }
    }
}
[{"level": "INFO", "message": "android.ndkVersion from module build.gradle is 26.1.10909125"}, {"level": "INFO", "message": "ndk.dir in local.properties is not set"}, {"level": "INFO", "message": "ANDROID_NDK_HOME environment variable is not set"}, {"level": "INFO", "message": "sdkFolder is C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\missingSdkDirectory"}, {"level": "INFO", "message": "NDK side-by-side folder from sdkFolder C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\missingSdkDirectory\\ndk does not exist"}, {"level": "INFO", "message": "Considering C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\missingSdkDirectory\\ndk-bundle in SDK ndk-bundle folder"}, {"level": "INFO", "message": "Rejected C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\missingSdkDirectory\\ndk-bundle in SDK ndk-bundle folder because that location has no source.properties"}, {"level": "WARN", "message": "Compatible side by side NDK version was not found for android.ndkVersion '26.1.10909125'"}]
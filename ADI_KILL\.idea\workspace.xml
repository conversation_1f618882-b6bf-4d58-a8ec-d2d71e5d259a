<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
  </component>
  <component name="AndroidLogFilters">
    <option name="TOOL_WINDOW_CONFIGURED_FILTER" value="Show only selected application" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="bab94809-ada2-462f-9724-7950506821f8" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$">
          <activation />
        </task>
        <task path="$PROJECT_DIR$/app">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="ADI_KILL" type="f1a62948:ProjectNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="ADI_KILL" type="f1a62948:ProjectNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="ADI_KILL" type="f1a62948:ProjectNode" />
                <item name="app" type="2d1252cf:ModuleNode" />
              </path>
              <path>
                <item name="" type="6a2764b6:ExternalProjectsStructure$RootNode" />
                <item name="ADI_KILL" type="f1a62948:ProjectNode" />
                <item name="app" type="2d1252cf:ModuleNode" />
                <item name="Tasks" type="e4a08cd1:TasksNode" />
              </path>
            </expand>
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="ProjectId" id="2xpJaemRT0CZLHZICBgP8loMAGQ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
  </component>
  <component name="RunManager">
    <configuration default="true" type="AndroidJUnit" factoryName="Android JUnit">
      <option name="TEST_OBJECT" value="class" />
      <option name="WORKING_DIRECTORY" value="$MODULE_DIR$" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App">
      <module name="app" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="SKIP_NOOP_APK_INSTALLATIONS" value="true" />
      <option name="FORCE_STOP_RUNNING_APP" value="true" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
      </Hybrid>
      <Java />
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Sample Java Methods" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="bab94809-ada2-462f-9724-7950506821f8" name="Default Changelist" comment="" />
      <created>1748629147115</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748629147115</updated>
    </task>
    <servers />
  </component>
  <component name="WindowStateProjectService">
    <state x="275" y="22" key="#Gradle" timestamp="1748635062332">
      <screen x="0" y="0" width="1366" height="728" />
    </state>
    <state x="275" y="22" key="#Gradle/0.0.1366.728@0.0.1366.728" timestamp="1748635062332" />
    <state x="435" y="167" key="#com.intellij.fileTypes.FileTypeChooser" timestamp="1749239910335">
      <screen x="0" y="0" width="1366" height="728" />
    </state>
    <state x="435" y="167" key="#com.intellij.fileTypes.FileTypeChooser/0.0.1366.728@0.0.1366.728" timestamp="1749239910335" />
    <state x="463" y="119" key="FileChooserDialogImpl" timestamp="1749386698625">
      <screen x="0" y="0" width="1366" height="728" />
    </state>
    <state x="463" y="119" key="FileChooserDialogImpl/0.0.1366.728@0.0.1366.728" timestamp="1749386698625" />
    <state width="643" height="121" key="GridCell.Tab.0.bottom" timestamp="1748667451951">
      <screen x="0" y="0" width="1366" height="728" />
    </state>
    <state width="643" height="121" key="GridCell.Tab.0.bottom/0.0.1366.728@0.0.1366.728" timestamp="1748667451951" />
    <state width="643" height="121" key="GridCell.Tab.0.center" timestamp="1748667451951">
      <screen x="0" y="0" width="1366" height="728" />
    </state>
    <state width="643" height="121" key="GridCell.Tab.0.center/0.0.1366.728@0.0.1366.728" timestamp="1748667451951" />
    <state width="643" height="121" key="GridCell.Tab.0.left" timestamp="1748667451950">
      <screen x="0" y="0" width="1366" height="728" />
    </state>
    <state width="643" height="121" key="GridCell.Tab.0.left/0.0.1366.728@0.0.1366.728" timestamp="1748667451950" />
    <state width="643" height="121" key="GridCell.Tab.0.right" timestamp="1748667451951">
      <screen x="0" y="0" width="1366" height="728" />
    </state>
    <state width="643" height="121" key="GridCell.Tab.0.right/0.0.1366.728@0.0.1366.728" timestamp="1748667451951" />
    <state x="13" y="13" key="SettingsEditor" timestamp="1748634791931">
      <screen x="0" y="0" width="1366" height="728" />
    </state>
    <state x="13" y="13" key="SettingsEditor/0.0.1366.728@0.0.1366.728" timestamp="1748634791931" />
    <state x="379" y="188" key="com.intellij.ide.util.TipDialog" timestamp="1749386238602">
      <screen x="0" y="0" width="1366" height="728" />
    </state>
    <state x="379" y="188" key="com.intellij.ide.util.TipDialog/0.0.1366.728@0.0.1366.728" timestamp="1749386238602" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="com.jetbrains.cidr.execution.debugger.OCBreakpointType">
          <url>file://$PROJECT_DIR$/app/src/main/jni/Server.cpp</url>
          <line>477</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>
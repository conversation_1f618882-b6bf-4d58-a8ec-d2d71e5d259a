apply plugin: 'com.android.application'

android {
    compileSdkVersion 29
    defaultConfig {
        applicationId "com.broken"
        minSdkVersion 19
        targetSdkVersion 29
        versionCode 1
        versionName "1.0"
        buildConfigField "String", "GITHUB_TOKEN", "\"${GITHUB_TOKEN}\""
        ndk {
            abiFilters 'armeabi-v7a'
        }
    }
    buildTypes {
        release {
            shrinkResources false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            shrinkResources false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    externalNativeBuild {
        ndkBuild {
            path file('src/main/jni/Android.mk')
        }
    }
    ndkVersion '26.1.10909125' // Ajuste conforme necessário
}

//dependencies must be placed below 'android' brackets to get it work on AIDE
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    // Add these:
    implementation 'com.squareup.okhttp3:okhttp:4.9.3'
    implementation 'com.google.code.gson:gson:2.9.1'
    implementation 'org.mindrot:jbcrypt:0.4'
    implementation 'at.favre.lib:bcrypt:0.9.0'
}

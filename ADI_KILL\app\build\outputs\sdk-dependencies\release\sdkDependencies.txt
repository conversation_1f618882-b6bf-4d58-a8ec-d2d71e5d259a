# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.9.3"
  }
  digests {
    sha256: "\223\354\326\313\241\235\207\334\317\345f\354\204\215\221\252\347\231\343\317\026\300\a\t5\216\246\233\331\"r\031"
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "2.8.0"
  }
  digests {
    sha256: "D\226\260ns\230/\315\330\2459?F\345\337,\342\372De\337X\225EL\254h\243/\t\273\310"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.4.10"
  }
  digests {
    sha256: "\001\354\260\227\202\300B\2711\301\203\232\317!\241\2104\v)]\005@\n\375n4\025\324G[\215\252"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.4.10"
  }
  digests {
    sha256: "F\201\362\3246\246\214u#Y]\204\355WX\3418/\235\240\366|\221\346\250Hi\rq\022t\376"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "13.0"
  }
  digests {
    sha256: "\254\342\241\r\310\342\325\3754\222^\312\300>I\210\262\300\370Qe\f\224\270\316\364\233\241\275\021\024x"
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.9.1"
  }
  digests {
    sha256: "7\2054\3439\346\346\325\v\0276\373:\273v\361\301]\033\343\364\301<\354mSd\022\342=\246\003"
  }
}
library {
  maven_library {
    groupId: "org.mindrot"
    artifactId: "jbcrypt"
    version: "0.4"
  }
  digests {
    sha256: "\341\203\366\365\224\004\374\036\022\a<\376\244\254\347\352\020<\220\004c\315!\373`\232|a~\315\366$"
  }
}
library {
  maven_library {
    groupId: "at.favre.lib"
    artifactId: "bcrypt"
    version: "0.9.0"
  }
  digests {
    sha256: "\346:\263\2112@\263\\r\036\234~\250]\177\222\033\252D\353\024\320\021\035\370\346\031\310\001\255\222\243"
  }
}
library {
  maven_library {
    groupId: "at.favre.lib"
    artifactId: "bytes"
    version: "1.3.0"
  }
  digests {
    sha256: "\035\245T_ @ yCs\276\3458\t\vx \351vs\370\312\352~\aH\263\256\017$\360!"
  }
}
library {
  digests {
    sha256: "\207|\346\2569\316lo%-\030w\030\317\215\324y\202p\356\210\233\370\305\360P5q\342=\221\373"
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 3
}
library_dependencies {
  library_index: 4
}
library_dependencies {
  library_index: 5
}
library_dependencies {
  library_index: 6
}
library_dependencies {
  library_index: 7
  library_dep_index: 8
}
library_dependencies {
  library_index: 8
}
library_dependencies {
  library_index: 9
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 6
  dependency_index: 7
  dependency_index: 9
}

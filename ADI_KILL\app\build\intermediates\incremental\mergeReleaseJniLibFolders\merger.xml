<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\ADI_KILL\ADI_KILL\app\src\main\jniLibs"><file name="armeabi-v7a/libbroken.so" path="C:\Users\<USER>\Downloads\ADI_KILL\ADI_KILL\app\src\main\jniLibs\armeabi-v7a\libbroken.so"/><file name="armeabi-v7a/libinjectEmulator.so" path="C:\Users\<USER>\Downloads\ADI_KILL\ADI_KILL\app\src\main\jniLibs\armeabi-v7a\libinjectEmulator.so"/></source></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Downloads\ADI_KILL\ADI_KILL\app\src\release\jniLibs"/></dataSet></merger>
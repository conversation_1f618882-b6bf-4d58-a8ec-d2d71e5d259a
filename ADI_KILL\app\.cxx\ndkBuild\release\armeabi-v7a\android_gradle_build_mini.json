{"buildFiles": ["C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk", "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Application.mk"], "cleanCommands": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\ndk-build.cmd NDK_PROJECT_PATH=null APP_BUILD_SCRIPT=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk NDK_APPLICATION_MK=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Application.mk APP_ABI=armeabi-v7a NDK_ALL_ABIS=armeabi-v7a NDK_DEBUG=0 APP_PLATFORM=android-21 NDK_OUT=C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/build/intermediates/ndkBuild/release/obj NDK_LIBS_OUT=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\build\\intermediates\\ndkBuild\\release\\lib clean"], "buildTargetsCommand": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\ndk-build.cmd NDK_PROJECT_PATH=null APP_BUILD_SCRIPT=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk NDK_APPLICATION_MK=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Application.mk APP_ABI=armeabi-v7a NDK_ALL_ABIS=armeabi-v7a NDK_DEBUG=0 APP_PLATFORM=android-21 NDK_OUT=C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/build/intermediates/ndkBuild/release/obj NDK_LIBS_OUT=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\build\\intermediates\\ndkBuild\\release\\lib {LIST_OF_TARGETS_TO_BUILD}", "libraries": {"hawdawdawdawda-release-armeabi-v7a": {"artifactName": "hawdaw<PERSON><PERSON><PERSON><PERSON>da", "buildCommand": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\ndk-build.cmd NDK_PROJECT_PATH=null APP_BUILD_SCRIPT=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk NDK_APPLICATION_MK=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Application.mk APP_ABI=armeabi-v7a NDK_ALL_ABIS=armeabi-v7a NDK_DEBUG=0 APP_PLATFORM=android-21 NDK_OUT=C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/build/intermediates/ndkBuild/release/obj NDK_LIBS_OUT=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\build\\intermediates\\ndkBuild\\release\\lib C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/build/intermediates/ndkBuild/release/obj/local/armeabi-v7a/libhawdawdawdawda.so", "abi": "armeabi-v7a", "output": "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\build\\intermediates\\ndkBuild\\release\\obj\\local\\armeabi-v7a\\libhawdawdawdawda.so", "runtimeFiles": []}, "jifjf-release-armeabi-v7a": {"artifactName": "jifjf", "buildCommand": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\ndk-build.cmd NDK_PROJECT_PATH=null APP_BUILD_SCRIPT=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Android.mk NDK_APPLICATION_MK=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\src\\main\\jni\\Application.mk APP_ABI=armeabi-v7a NDK_ALL_ABIS=armeabi-v7a NDK_DEBUG=0 APP_PLATFORM=android-21 NDK_OUT=C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/build/intermediates/ndkBuild/release/obj NDK_LIBS_OUT=C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\build\\intermediates\\ndkBuild\\release\\lib C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/build/intermediates/ndkBuild/release/obj/local/armeabi-v7a/libjifjf.so", "abi": "armeabi-v7a", "output": "C:\\Users\\<USER>\\Downloads\\ADI_KILL\\ADI_KILL\\app\\build\\intermediates\\ndkBuild\\release\\obj\\local\\armeabi-v7a\\libjifjf.so", "runtimeFiles": []}}}
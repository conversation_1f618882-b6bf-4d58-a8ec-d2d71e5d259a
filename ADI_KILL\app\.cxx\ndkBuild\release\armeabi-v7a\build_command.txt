Executable : C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\ndk-build.cmd
arguments : 
NDK_PROJECT_PATH=null
APP_BUILD_SCRIPT=C:\Users\<USER>\Downloads\ADI_KILL\ADI_KILL\app\src\main\jni\Android.mk
NDK_APPLICATION_MK=C:\Users\<USER>\Downloads\ADI_KILL\ADI_KILL\app\src\main\jni\Application.mk
APP_ABI=armeabi-v7a
NDK_ALL_ABIS=armeabi-v7a
NDK_DEBUG=0
APP_PLATFORM=android-21
NDK_OUT=C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/build/intermediates/ndkBuild/release/obj
NDK_LIBS_OUT=C:\Users\<USER>\Downloads\ADI_KILL\ADI_KILL\app\build\intermediates\ndkBuild\release\lib
APP_SHORT_COMMANDS=false
LOCAL_SHORT_COMMANDS=false
-B
-n
jvmArgs : 

Build command args:

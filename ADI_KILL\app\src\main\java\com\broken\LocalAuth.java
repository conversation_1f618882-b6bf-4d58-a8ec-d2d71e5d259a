package com.broken;

import android.os.Handler;
import android.os.Looper;

public class LocalAuth {

    // بيانات المصادقة المحلية
    private static final String LOCAL_USERNAME = "admin";
    private static final String LOCAL_PASSWORD = "123456";

    public interface Callback {
        void onSuccess();
        void onFailure(String reason);
    }

    public static void login(final String username, final String password, final Callback callback) {
        // تشغيل المصادقة في خيط منفصل لمحاكاة السلوك الأصلي
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    // محاكاة تأخير بسيط
                    Thread.sleep(500);
                    
                    boolean success = false;
                    String error = null;
                    
                    // التحقق من بيانات المصادقة
                    if (username == null || username.trim().isEmpty()) {
                        error = "Username is required";
                    } else if (password == null || password.trim().isEmpty()) {
                        error = "Password is required";
                    } else if (!username.equals(LOCAL_USERNAME)) {
                        error = "Invalid username";
                    } else if (!password.equals(LOCAL_PASSWORD)) {
                        error = "Invalid password";
                    } else {
                        success = true;
                    }
                    
                    final boolean finalSuccess = success;
                    final String finalError = error;
                    
                    // العودة للخيط الرئيسي لتحديث واجهة المستخدم
                    new Handler(Looper.getMainLooper()).post(new Runnable() {
                        @Override
                        public void run() {
                            if (finalSuccess) {
                                callback.onSuccess();
                            } else {
                                callback.onFailure(finalError != null ? finalError : "Authentication failed");
                            }
                        }
                    });
                    
                } catch (final Exception e) {
                    new Handler(Looper.getMainLooper()).post(new Runnable() {
                        @Override
                        public void run() {
                            callback.onFailure("Authentication error: " + e.getMessage());
                        }
                    });
                }
            }
        }).start();
    }
}

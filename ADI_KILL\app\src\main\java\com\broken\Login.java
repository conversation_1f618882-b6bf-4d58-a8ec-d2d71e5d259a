package com.broken;

import android.app.Activity;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.text.method.PasswordTransformationMethod;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.*;

import android.graphics.drawable.GradientDrawable;
import android.graphics.drawable.RippleDrawable;

public class Login {

    private Context context;
    private Utils utils;
    private ImageString imageString;
    private int injectType;

    public Login(Context glob_Context) {
        this.context = glob_Context;
        this.utils = new Utils(context);
        this.imageString = new ImageString();
        Init();
    }

    private void Init() {
        final String androidId = Settings.Secure.getString(
                context.getContentResolver(),
                Settings.Secure.ANDROID_ID
        );

        LinearLayout container = new LinearLayout(context);
        container.setBackgroundColor(0xFFE9E9E9);
        container.setOrientation(LinearLayout.VERTICAL);
        container.setGravity(Gravity.CENTER);

        GradientDrawable gradient_container_login = new GradientDrawable();
        gradient_container_login.setCornerRadius(15);
        gradient_container_login.setColor(0xFFFFFFFF);
        gradient_container_login.setStroke(2, 0xCC919191);

        LinearLayout container_login = new LinearLayout(context);
        container_login.setLayoutParams(new LinearLayout.LayoutParams(650, 400));
        container_login.setOrientation(LinearLayout.VERTICAL);
        container_login.setGravity(Gravity.CENTER_HORIZONTAL);
        container_login.setBackground(gradient_container_login);
        container_login.setElevation(10.f);

        GradientDrawable gradient_line_color = new GradientDrawable();
        gradient_line_color.setColor(0xFFCC0000);

        LinearLayout line_color = new LinearLayout(context);
        line_color.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, utils.FixDP(8)));
        line_color.setBackground(gradient_line_color);

        LinearLayout container_top = new LinearLayout(context);
        container_top.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, utils.FixDP(60)));
        container_top.setGravity(Gravity.CENTER);

        ImageBase64 image_icon = new ImageBase64(context);
        image_icon.setImageBase64(imageString.icon_image);
        image_icon.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, utils.FixDP(55)));

        LinearLayout line_separator_1 = new LinearLayout(context);
        line_separator_1.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, utils.FixDP(2)));
        line_separator_1.setBackgroundColor(0xCC919191);

        LinearLayout container_center = new LinearLayout(context);
        container_center.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT, 1));
        container_center.setPadding(utils.FixDP(8), utils.FixDP(8), utils.FixDP(8), utils.FixDP(8));
        container_center.setOrientation(LinearLayout.VERTICAL);
        container_center.setGravity(Gravity.CENTER);

        GradientDrawable gradient_input = new GradientDrawable();
        gradient_input.setColor(0x00000000);
        gradient_input.setStroke(2, 0xFF919191);
        gradient_input.setCornerRadius(8);

        LinearLayout.LayoutParams layoutParams_input = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, utils.FixDP(40));
        layoutParams_input.setMargins(0, utils.FixDP(8), 0, 0);

        final EditText input_username = new EditText(context);
        input_username.setLayoutParams(layoutParams_input);
        input_username.setHint("Insert your username");
        input_username.setText("admin"); // ملء تلقائي
        input_username.setTextSize(14);
        input_username.setSingleLine();
        input_username.setPadding(utils.FixDP(8), 0, utils.FixDP(8), 0);
        input_username.setBackground(gradient_input);

        final EditText input_password = new EditText(context);
        input_password.setLayoutParams(layoutParams_input);
        input_password.setHint("Insert your password");
        input_password.setText("123456"); // ملء تلقائي
        input_password.setSingleLine();
        input_password.setTextSize(14);
        input_password.setPadding(utils.FixDP(8), 0, utils.FixDP(8), 0);
        input_password.setBackground(gradient_input);
        input_password.setTransformationMethod(PasswordTransformationMethod.getInstance());

        // إضافة خيار نوع المصادقة
        RadioGroup authTypeGroup = new RadioGroup(context);
        authTypeGroup.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        authTypeGroup.setPadding(0, utils.FixDP(8), 0, 0);
        authTypeGroup.setOrientation(LinearLayout.HORIZONTAL);

        final RadioButton radioButton_local = new RadioButton(context);
        radioButton_local.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        radioButton_local.setPadding(0, 0, utils.FixDP(8), 0);
        radioButton_local.setText("LOCAL AUTH");
        radioButton_local.setTextSize(12);
        radioButton_local.setChecked(true); // تحديد المصادقة المحلية كافتراضي
        if (radioButton_local.getButtonDrawable() != null)
            radioButton_local.getButtonDrawable().setColorFilter(0xFFA70000, PorterDuff.Mode.SRC_IN);

        final RadioButton radioButton_online = new RadioButton(context);
        radioButton_online.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        radioButton_online.setText("ONLINE AUTH");
        radioButton_online.setTextSize(12);
        if (radioButton_online.getButtonDrawable() != null)
            radioButton_online.getButtonDrawable().setColorFilter(0xFFA70000, PorterDuff.Mode.SRC_IN);

        RadioGroup radioGroup = new RadioGroup(context);
        radioGroup.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        radioGroup.setPadding(0, utils.FixDP(8), 0, 0);
        radioGroup.setOrientation(LinearLayout.HORIZONTAL);

        final RadioButton radioButton_root = new RadioButton(context);
        radioButton_root.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        radioButton_root.setPadding(0, 0, utils.FixDP(8), 0);
        radioButton_root.setText("VMOS");
        radioButton_root.setTextSize(12);
        radioButton_root.setChecked(true); // تحديد VMOS افتراضياً
        if (radioButton_root.getButtonDrawable() != null)
            radioButton_root.getButtonDrawable().setColorFilter(0xFFA70000, PorterDuff.Mode.SRC_IN);

        final RadioButton radioButton_emulator = new RadioButton(context);
        radioButton_emulator.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        radioButton_emulator.setText("EMULATOR");
        radioButton_emulator.setTextSize(12);
        if (radioButton_emulator.getButtonDrawable() != null)
            radioButton_emulator.getButtonDrawable().setColorFilter(0xFFA70000, PorterDuff.Mode.SRC_IN);

        LinearLayout container_bottom = new LinearLayout(context);
        container_bottom.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, utils.FixDP(45)));
        container_bottom.setPadding(utils.FixDP(8), 0, utils.FixDP(8), utils.FixDP(8));

        GradientDrawable gradientDrawable_inject_close = new GradientDrawable();
        gradientDrawable_inject_close.setColor(0xFFA70000);
        gradientDrawable_inject_close.setCornerRadius(utils.FixDP(8));

        RippleDrawable rippleDrawable = new RippleDrawable(ColorStateList.valueOf(0xFF111111), gradientDrawable_inject_close, null);

        Button login = new Button(context);
        login.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
        login.setPadding(0, 0, 0, 0);
        login.setBackground(rippleDrawable);
        login.setText("LOGIN");
        login.setTextColor(0xFFFFFFFF);
        login.setTextSize(12);

        login.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!radioButton_root.isChecked() && !radioButton_emulator.isChecked()) {
                    Toast.makeText(context, "Please, select injection type!", Toast.LENGTH_SHORT).show();
                    return;
                }

                // تسجيل دخول تلقائي - ملء البيانات تلقائياً
                final String username = "admin";
                final String password = "123456";

                // ملء الحقول تلقائياً (اختياري للعرض)
                input_username.setText(username);
                input_password.setText(password);

                injectType = radioButton_root.isChecked() ? 0 : 1;

                // التحقق من نوع المصادقة المحدد
                if (radioButton_local.isChecked()) {
                    // استخدام المصادقة المحلية
                    LocalAuth.login(username, password, new LocalAuth.Callback() {
                        @Override
                        public void onSuccess() {
                            new Handler(Looper.getMainLooper()).post(new Runnable() {
                                @Override
                                public void run() {
                                    Toast.makeText(context, "Local authentication successful!", Toast.LENGTH_SHORT).show();
                                    new Menu(context, injectType);
                                }
                            });
                        }

                        @Override
                        public void onFailure(final String reason) {
                            new Handler(Looper.getMainLooper()).post(new Runnable() {
                                @Override
                                public void run() {
                                    Toast.makeText(context, reason, Toast.LENGTH_SHORT).show();
                                }
                            });
                        }
                    });
                } else {
                    // استخدام المصادقة عبر الإنترنت (النظام الأصلي)
                    Auth.login(username, password, androidId, new Auth.Callback() {
                        @Override
                        public void onSuccess() {
                            new Handler(Looper.getMainLooper()).post(new Runnable() {
                                @Override
                                public void run() {
                                    new Menu(context, injectType);
                                }
                            });
                        }

                        @Override
                        public void onFailure(final String reason) {
                            new Handler(Looper.getMainLooper()).post(new Runnable() {
                                @Override
                                public void run() {
                                    Toast.makeText(context, reason, Toast.LENGTH_SHORT).show();
                                }
                            });
                        }
                    });
                }
            }
        });

        TextView text_copyright = new TextView(context);
        text_copyright.setPadding(0, utils.FixDP(8), 0, 0);
        text_copyright.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        text_copyright.setText("Copyright © Ld AimKill -2024 ");
        text_copyright.setTextSize(12);

        ((Activity) context).setContentView(container);
        container.addView(container_login);
        container_login.addView(line_color);
        container_login.addView(container_top);
        container_top.addView(image_icon);
        container_login.addView(line_separator_1);
        container_login.addView(container_center);
        container_center.addView(input_username);
        container_center.addView(input_password);
        container_center.addView(authTypeGroup);
        authTypeGroup.addView(radioButton_local);
        authTypeGroup.addView(radioButton_online);
        container_center.addView(radioGroup);
        radioGroup.addView(radioButton_root);
        radioGroup.addView(radioButton_emulator);
        container_login.addView(container_bottom);
        container_bottom.addView(login);
        container.addView(text_copyright);
    }
}
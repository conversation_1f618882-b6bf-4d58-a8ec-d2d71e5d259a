In file included from C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/src/main/jni/Server.cpp:14:
C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/src/main/jni/Tools/Includes/Utils.h:11:10: warning: non-portable path to file '"Unity/Unity.h"'; specified path differs in case from file name on disk [-Wnonportable-include-path]
#include "Unity/unity.h"
         ^~~~~~~~~~~~~~~
         "Unity/Unity.h"
In file included from C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/src/main/jni/Server.cpp:20:
C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/src/main/jni/Hack/il2cpp.h:184814:2: warning: declaration does not declare anything [-Wmissing-declarations]
        bool thread_local;
        ^~~~~~~~~~~~~~~~~
C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/src/main/jni/Hack/il2cpp.h:543667:2: warning: declaration does not declare anything [-Wmissing-declarations]
        float const;
        ^~~~~~~~~~~
In file included from C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/src/main/jni/Server.cpp:23:
In file included from C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/src/main/jni/Tools/Includes/Macros.h:24:
C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/src/main/jni/Tools/Substrate/CydiaSubstrate.h:46:9: warning: '_extern' macro redefined [-Wmacro-redefined]
#define _extern \
        ^
C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/src/main/jni/Tools/Substrate/SubstrateHook.h:7:9: note: previous definition is here
#define _extern extern "C" __attribute__((__visibility__("hidden")))
        ^
4 warnings generated.
C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/src/main/jni/Tools/KittyMemory/KittyMemory.cpp:163:20: warning: format specifies type 'unsigned long long *' but the argument has type 'unsigned int *' [-Wformat]
                   &map.startAddress, &map.endAddress,
                   ^~~~~~~~~~~~~~~~~
1 warning generated.
C:/Users/<USER>/Downloads/ADI_KILL/ADI_KILL/app/src/main/jni/Tools/SOCKET/server.cpp:1:10: warning: non-portable path to file '"Server.h"'; specified path differs in case from file name on disk [-Wnonportable-include-path]
#include "server.h"
         ^~~~~~~~~~
         "Server.h"
1 warning generated.

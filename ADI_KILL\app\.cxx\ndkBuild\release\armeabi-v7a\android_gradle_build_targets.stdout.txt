[armeabi-v7a] Compile++ arm  : hawdawdawdawda <= Client.cpp
[armeabi-v7a] Compile++ arm  : hawdawdawdawda <= client.cpp
[armeabi-v7a] SharedLibrary  : libhawdawdawdawda.so
[armeabi-v7a] Compile++ thumb: jifjf <= Server.cpp
[armeabi-v7a] Compile++ thumb: jifjf <= KittyArm64.cpp
[armeabi-v7a] Compile++ thumb: jifjf <= KittyMemory.cpp
[armeabi-v7a] Compile++ thumb: jifjf <= KittyScanner.cpp
[armeabi-v7a] Compile++ thumb: jifjf <= KittyUtils.cpp
[armeabi-v7a] Compile++ thumb: jifjf <= MemoryBackup.cpp
[armeabi-v7a] Compile++ thumb: jifjf <= MemoryPatch.cpp
[armeabi-v7a] Compile++ thumb: jifjf <= server.cpp
[armeabi-v7a] SharedLibrary  : libjifjf.so
